{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "useDefineForClassFields": false, "downlevelIteration": true, "verbatimModuleSyntax": true, "importHelpers": true, "target": "ES2020", "module": "ESNext", "lib": ["ES2020", "DOM"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@libs": ["libs/index.ts"]}}, "exclude": ["node_modules", "tmp"]}