import { Module, Global } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { NotificationRepository } from './repositories/notification.repository';
import { NotificationResultRepository } from './repositories/notification-result.repository';
import { NotificationTemplateRepository } from './repositories/notification-template.repository';

@Global()
@Module({
  providers: [
    PrismaService,
    NotificationRepository,
    NotificationResultRepository,
    NotificationTemplateRepository,
  ],
  exports: [
    PrismaService,
    NotificationRepository,
    NotificationResultRepository,
    NotificationTemplateRepository,
  ],
})
export class DatabaseModule {}
